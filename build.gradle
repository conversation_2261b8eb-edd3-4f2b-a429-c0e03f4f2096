plugins {
    id "java"
    id "idea"
    id "org.jetbrains.kotlin.jvm"
    id "com.github.johnrengelman.shadow" version "6.1.0"
    id "net.minecraftforge.gradle.forge"
    id "org.spongepowered.mixin"
    id "com.gorylenko.gradle-git-properties" version "2.4.2"
}

repositories {
    mavenLocal()
    mavenCentral()
    maven { url = "https://repo.spongepowered.org/repository/maven-public/" }
    maven { url = "https://jitpack.io/" }
}

archivesBaseName = project.archives_base_name
version = project.mod_version
group = project.maven_group

sourceCompatibility = targetCompatibility = 1.8
compileJava.options.encoding = "UTF-8"

compileKotlin {
    kotlinOptions {
        jvmTarget = "1.8"
    }
}

minecraft {
    version = "1.8.9-11.15.1.2318-1.8.9"
    runDir = "run"
    mappings = "stable_22"
    makeObfSourceJar = false
    clientJvmArgs += ["-Dfml.coreMods.load=net.ccbluex.liquidbounce.injection.forge.MixinLoader", "-Xmx4096m", "-Xms1024m", "-Ddev-mode"]
}

configurations {
    runtimeOnly.canBeResolved = true
}

dependencies {
    implementation("org.spongepowered:mixin:0.7.11-SNAPSHOT") {
        transitive = false
        exclude module: "guava"
        exclude module: "commons-io"
        exclude module: "gson"
        exclude module: "launchwrapper"
        exclude module: "log4j-core"
        exclude module: "slf4j-api"
    }

    annotationProcessor("org.spongepowered:mixin:0.7.11-SNAPSHOT")

    implementation "com.jagrosh:DiscordIPC:0.4"

    implementation("com.github.CCBlueX:Elixir:1.2.6") {
        exclude module: "kotlin-stdlib"
        exclude module: "authlib"
    }

    implementation 'com.github.TheAltening:TheAltening4j:d0771f42d3'
    implementation 'com.github.TheAltening:API-Java-AuthLib:63a9702615'

    implementation("org.knowm.xchart:xchart:3.8.8")

    // HTTP Client
    implementation("com.squareup.okhttp3:okhttp:5.0.0-alpha.14") {
        exclude module: "kotlin-stdlib"
    }

    // Kotlin
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk8:$kotlin_version"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-core:$kotlin_coroutines_version"
    implementation "org.jetbrains.kotlinx:kotlinx-coroutines-test:$kotlin_coroutines_version"

    // Swing LookAndFeel
    implementation "com.formdev:flatlaf:3.5.4"

    implementation fileTree(include: ["*.jar"], dir: "libs")
}

shadowJar {
    archiveClassifier.set("")
    duplicatesStrategy DuplicatesStrategy.EXCLUDE

    exclude "LICENSE.txt"

    exclude "META-INF/maven/**"
    exclude "META-INF/versions/**"

    exclude "org/apache/log4j/**"
    exclude "org/apache/commons/**"
    exclude "org/junit/**"
}

processResources {
    inputs.property "version", project.version
    inputs.property "mcversion", project.minecraft.version

    filesMatching("mcmod.info") {
        expand "version": project.version, "mcversion": project.minecraft.version
    }

    rename "(.+_at.cfg)", "META-INF/\$1"
}

task moveResources {
    doLast {
        copy {
            from "${buildDir}/resources/main"
            into "${buildDir}/classes/java"
        }
    }
}

moveResources.dependsOn(processResources)
classes.dependsOn(moveResources)

jar {
    manifest.attributes(
            "FMLCorePlugin": "net.ccbluex.liquidbounce.injection.forge.MixinLoader",
            "FMLCorePluginContainsFMLMod": true,
            "ForceLoadAsMod": true,
            "MixinConfigs": "liquidbounce.forge.mixins.json",
            "ModSide": "CLIENT",
            "TweakClass": "org.spongepowered.asm.launch.MixinTweaker",
            "TweakOrder": "0",
            "FMLAT": "liquidbounce_at.cfg",
            "Main-Class": "net.ccbluex.liquidinstruction.LiquidInstructionKt",
    )

    enabled = false
}

mixin {
    disableRefMapWarning = true
    defaultObfuscationEnv searge
    add sourceSets.main, "liquidbounce.mixins.refmap.json"
}

reobf {
    shadowJar {
        mappingType = "SEARGE"
    }
}

jar.dependsOn("shadowJar")

tasks.reobfShadowJar.mustRunAfter shadowJar

task copyZipInclude(type: Copy) {
    from 'zip_include/'
    into 'build/libs/zip'
}

build.dependsOn copyZipInclude
