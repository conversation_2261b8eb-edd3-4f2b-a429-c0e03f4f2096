# LiquidBounce Legacy

## Discontinuation Notice

[Legacy](https://github.com/CCBlueX/LiquidBounce/tree/legacy) development was maintained by the community from 2020 to 2025. However, Legacy is now discontinued in favor of [Nextgen](https://github.com/CCBlueX/LiquidBounce/tree/nextgen). Since 2020, the project was officially discontinued by us, [CCBlueX](https://ccbluex.net), which started the development in 2015.

**b100 is the final release published by our community maintainers, and no further pull requests will be accepted.** A huge thanks to everyone who contributed to the project over the years, with special recognition to our lead maintainers, @EclipsesDev and @mems01.

> "I have started LiquidBounce in 2015, and since 2020 I have been working on a new code base, Nextgen, which left the Legacy code base to the community.
> A huge thank you for the community maintainers who have kept this branch of the client alive for so long, while our CCBlueX team was able to focus on our new branch.
> Looking at the current state, I'm not entirely happy with the direction this branch has taken, but I'm grateful for the work that has been done and the time that has been invested in it.
> I'm looking forward to the future of LiquidBounce and hope that we can merge our efforts in the future to create a better client for everyone."
>
> — <PERSON><PERSON><PERSON>, <PERSON>

> "With this being the final release of Legacy, I’m grateful for how much this client has progressed.
> It has not only improved significantly over the previous version but also helped me grow as a developer, pushing me to learn and do better.
> I’d like to thank the CCBlueX team for their client support throughout this journey and @mems01 for his help during the development."
>
> — <PERSON>sDev, Lead Community Maintainer

> "This is the final Legacy version, meaning that the development continuation by the community (me and @<PERSON>sDev) is coming to a stop, permanently.
> I am proud to have reached this moment and this far with this client's development as it taught me new things everyday.
> I thank the CCBlueX team for allowing me in the first place to continue the development of this client, and @EclipsesDev for helping me do so.
> It was a good run."
>
> — mems01, Lead Community Maintainer

We are grateful for the support we received from the community and hope that you will continue to support us in the future on our new code base, [Nextgen](https://github.com/CCBlueX/LiquidBounce/tree/nextgen) that will continue to be maintained by us.
