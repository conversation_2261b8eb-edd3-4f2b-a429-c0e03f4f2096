 ▄▄▄     ▄▄▄ ▄▄▄▄▄▄▄ ▄▄   ▄▄ ▄▄▄ ▄▄▄▄▄▄  ▄▄▄▄▄▄▄ ▄▄▄▄▄▄▄ ▄▄   ▄▄ ▄▄    ▄ ▄▄▄▄▄▄▄ ▄▄▄▄▄▄▄ 
█   █   █   █       █  █ █  █   █      ██  ▄    █       █  █ █  █  █  █ █       █       █
█   █   █   █   ▄   █  █ █  █   █  ▄    █ █▄█   █   ▄   █  █ █  █   █▄█ █       █    ▄▄▄█
█   █   █   █  █ █  █  █▄█  █   █ █ █   █       █  █ █  █  █▄█  █       █     ▄▄█   █▄▄▄ 
█   █▄▄▄█   █  █▄█  █       █   █ █▄█   █  ▄   ██  █▄█  █       █  ▄    █    █  █    ▄▄▄█
█       █   █      ██       █   █       █ █▄█   █       █       █ █ █   █    █▄▄█   █▄▄▄ 
█▄▄▄▄▄▄▄█▄▄▄█▄▄▄▄██▄█▄▄▄▄▄▄▄█▄▄▄█▄▄▄▄▄▄██▄▄▄▄▄▄▄█▄▄▄▄▄▄▄█▄▄▄▄▄▄▄█▄█  █▄▄█▄▄▄▄▄▄▄█▄▄▄▄▄▄▄█
a open-source minecraft hacked-client for 1.8.9.
https://liquidbounce.net/

➔ How to install on Forge
1. Install Forge for 1.8.9 (from https://files.minecraftforge.net/net/minecraftforge/forge/index_1.8.9.html).
2. Create a folder called "mods" inside in the ".minecraft" directory.
3. Copy "liquidbounce.jar" into the mods folder.
4. RECOMMENDED: Install OptiFine mod for improved performance (from https://optifine.net/downloads)
5. RECOMMENDED: Install ViaForge mod to join any 1.7-1.18.2 server (from https://www.curseforge.com/minecraft/mc-mods/viaforge)
7. Launch Forge through the Minecraft launcher.

➔ Reminder for nightly builds
This is a development build. Use these versions only if you know what you are doing! Bugs can and will occur!

➔ How to report issues
If you found an issue with this version, please report it on our GitHub Issues:
https://github.com/CCBlueX/LiquidBounce/issues

Make sure it includes all required information, so we can reproduce it on our own and solve the issue.
In case of an error, please always provide a full log and crash log. (.minecraft/logs/latest.log)

➔ Support
If you need support, join our Guilded below. Feel free to tell us your question in the support channel, no need to ask for asking. There will always be people helping out.

Please don't contact us in direct messages, when you're looking for support.

➔ Do you want to contribute or want to know how our client is written?
Feel free to check the source code on GitHub:
https://github.com/CCBlueX/LiquidBounce

➔ Links
Our website: https://ccbluex.net/
Our youtube: https://youtube.com/ccbluex
Our twitter: https://twitter.com/ccbluex
Our guilded: https://www.guilded.gg/CCBlueX

➔ License
This project is subject to the GNU General Public License v3.0. This does only apply for source code located directly in this clean repository. During the development and compilation process, additional source code may be used to which we have obtained no rights. Such code is not covered by the GPL license.

Copyright © 2015 - 2025 | CCBlueX | All rights reserved.