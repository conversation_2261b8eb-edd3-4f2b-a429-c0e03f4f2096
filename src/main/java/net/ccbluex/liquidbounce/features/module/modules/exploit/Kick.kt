/*
 * LiquidBounce Hacked Client
 * A free open source mixin-based injection hacked client for Minecraft using Minecraft Forge.
 * https://github.com/CCBlueX/LiquidBounce/
 */
package net.ccbluex.liquidbounce.features.module.modules.exploit

import net.ccbluex.liquidbounce.features.module.Category
import net.ccbluex.liquidbounce.features.module.Module
import net.ccbluex.liquidbounce.utils.client.PacketUtils.sendPacket
import net.ccbluex.liquidbounce.utils.client.chat
import net.ccbluex.liquidbounce.utils.kotlin.RandomUtils.nextInt
import net.minecraft.network.play.client.C02PacketUseEntity
import net.minecraft.network.play.client.C02PacketUseEntity.Action.ATTACK
import net.minecraft.network.play.client.C02PacketUseEntity.Action.INTERACT
import net.minecraft.network.play.client.C03PacketPlayer.C04PacketPlayerPosition
import net.minecraft.network.play.client.C03PacketPlayer.C05PacketPlayerLook
import net.minecraft.network.play.client.C09PacketHeldItemChange
import kotlin.random.Random.Default.nextBoolean


object Kick : Module("Kick", Category.EXPLOIT, canBeEnabled = false, subjective = true) {

    private val mode by choices(
        "Mode",
        arrayOf("Quit", "InvalidPacket", "SelfHurt", "SelfInteract", "IllegalChat", "PacketSpam", "IllegalHeldItem", "IllegalPlayerLook"),
        "Quit"
    )

    override fun onEnable() {
        if (mc.isIntegratedServerRunning) {
            chat("§c§lError: §aYou can't enable §c§l'Kick' §ain SinglePlayer.")
            return
        }

        when (mode.lowercase()) {
            "quit" -> mc.theWorld.sendQuittingDisconnectingPacket()
            "invalidpacket" -> sendPacket(
                C04PacketPlayerPosition(
                    Double.NaN,
                    Double.NEGATIVE_INFINITY,
                    Double.POSITIVE_INFINITY,
                    !mc.thePlayer.onGround
                )
            )

            "selfhurt" -> sendPacket(C02PacketUseEntity(mc.thePlayer, ATTACK))
            "selfinteract" -> sendPacket(C02PacketUseEntity(mc.thePlayer, INTERACT))
            "illegalchat" -> mc.thePlayer.sendChatMessage(nextInt().toString() + "§§§" + nextInt())
            "packetspam" -> {
                repeat(9999) {
                    sendPacket(C04PacketPlayerPosition(it.toDouble(), it.toDouble(), it.toDouble(), nextBoolean()))
                }
            }

            "illegalhelditem" -> sendPacket(C09PacketHeldItemChange(-1))
            "illegalplayerlook" -> sendPacket(C05PacketPlayerLook(Float.NEGATIVE_INFINITY, Float.POSITIVE_INFINITY, !mc.thePlayer.onGround))
        }
    }

}
