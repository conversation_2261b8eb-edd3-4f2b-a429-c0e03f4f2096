/*
 * LiquidBounce Hacked Client
 * A free open source mixin-based injection hacked client for Minecraft using Minecraft Forge.
 * https://github.com/CCBlueX/LiquidBounce/
 */
package net.ccbluex.liquidbounce.features.module.modules.exploit

import net.ccbluex.liquidbounce.event.PacketEvent
import net.ccbluex.liquidbounce.event.handler
import net.ccbluex.liquidbounce.features.module.Category
import net.ccbluex.liquidbounce.features.module.Module
import net.minecraft.network.play.client.C03PacketPlayer

object NoPitchLimit : Module("NoPitchLimit", Category.EXPLOIT, gameDetecting = false) {

    private val serverSide by boolean("ServerSide", true)

    val onPacket = handler<PacketEvent> { e ->
        if (serverSide)
            return@handler

        if (e.packet is C03PacketPlayer && e.packet.rotating) {
            e.packet.pitch = e.packet.pitch.coerceIn(-90F, 90F)
        }
    }

}