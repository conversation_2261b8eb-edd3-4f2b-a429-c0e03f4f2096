/*
 * LiquidBounce Hacked Client
 * A free open source mixin-based injection hacked client for Minecraft using Minecraft Forge.
 * https://github.com/CCBlueX/LiquidBounce/
 */
package net.ccbluex.liquidbounce.features.module.modules.exploit

import net.ccbluex.liquidbounce.event.EventState
import net.ccbluex.liquidbounce.event.PacketEvent
import net.ccbluex.liquidbounce.event.handler
import net.ccbluex.liquidbounce.features.module.Module
import net.ccbluex.liquidbounce.features.module.Category
import net.ccbluex.liquidbounce.utils.client.PacketUtils.sendPacket
import net.ccbluex.liquidbounce.utils.client.PacketUtils.sendPackets
import net.ccbluex.liquidbounce.utils.extensions.component1
import net.ccbluex.liquidbounce.utils.extensions.component2
import net.ccbluex.liquidbounce.utils.extensions.component3
import net.minecraft.client.entity.EntityPlayerSP
import net.minecraft.network.play.client.C03PacketPlayer.C04PacketPlayerPosition
import net.minecraft.network.play.client.C03PacketPlayer.C06PacketPlayerPosLook
import net.minecraft.network.play.server.S19PacketEntityStatus

object Damage : Module("Damage", Category.EXPLOIT, canBeEnabled = false) {

    private val mode by choices("Mode", arrayOf("Fake", "NCP", "AAC", "Verus"), "NCP")

    // Verus
    private val verusMode by choices("VerusMode",
        arrayOf("Default", "Damage1", "Damage2", "Damage3", "Damage4", "CustomDamage"), "Damage1"
    ) { mode == "Verus" }
    private val customPacket1Clip by float("CustomDamage-Packet1Clip", 4f, 0f..5f)
    { mode == "Verus" && verusMode == "CustomDamage" }
    private val customPacket2Clip by float("CustomDamage-Packet2Clip", -0.2f, -1f..5f)
    { mode == "Verus" && verusMode == "CustomDamage" }
    private val customPacket3Clip by float("CustomDamage-Packet3Clip", 0.5f, 0f..5f)
    { mode == "Verus" && verusMode == "CustomDamage" }

    // NCP
    private val ncpMode by choices("NCPMode", arrayOf("Glitch", "JumpPacket"), "Glitch") { mode == "NCP" }

    // General Settings
    private val damageAmount by int("Damage", 1, 1..20) { mode in arrayOf("NCP", "AAC") }
    private val onlyOnGround by boolean("OnlyGround", true)

    private val jumpYPositions = doubleArrayOf(
        0.42, 0.75, 1.0, 1.17,
        1.25, 1.25, 1.17, 1.02,
        0.78, 0.48, 0.10, 0.0
    )

    override fun onEnable() {
        val player = mc.thePlayer ?: return

        if (onlyOnGround && !player.onGround) return

        when (mode.lowercase()) {
            "ncp" -> handleNCPDamage(player)
            "aac" -> player.motionY = 5 * damageAmount.toDouble()
            "verus" -> handleVerusDamage(player)
        }
    }

    val onPacket = handler<PacketEvent> { event ->
        if (mode != "Fake") return@handler

        val player = mc.thePlayer ?: return@handler
        val packet = event.packet

        if (packet is S19PacketEntityStatus && event.eventType == EventState.RECEIVE
            && packet.opCode == 2.toByte() && packet.entityId == player.entityId) {

            if (!event.isCancelled) {
                player.handleStatusUpdate(2.toByte())
            }
        }
    }

    private fun handleNCPDamage(player: EntityPlayerSP) {
        val (x, y, z) = player

        when (ncpMode.lowercase()) {
            "glitch" -> {
                repeat(65 * damageAmount) {
                    sendPackets(
                        C04PacketPlayerPosition(x, y + 0.049, z, false),
                        C04PacketPlayerPosition(x, y, z, false)
                    )
                }
                sendPacket(C04PacketPlayerPosition(x, y, z, true))
            }
            "jumppacket" -> {
                repeat(4 * damageAmount) {
                    jumpYPositions.forEach { yOffset ->
                        sendPacket(C04PacketPlayerPosition(x, y + yOffset, z, false))
                    }
                    sendPacket(C04PacketPlayerPosition(x, y, z, false))
                }
                sendPacket(C04PacketPlayerPosition(x, y, z, true))
            }
        }
    }

    private fun handleVerusDamage(player: EntityPlayerSP) {
        val (x, y, z) = player
        when (verusMode.lowercase()) {
            "default" -> {
                sendPackets(
                    C04PacketPlayerPosition(x, y + 3.0001, z, false),
                    C06PacketPlayerPosLook(x, y, z, player.rotationYaw, player.rotationPitch, false),
                    C06PacketPlayerPosLook(x, y, z, player.rotationYaw, player.rotationPitch, true)
                )
            }
            "damage1" -> sendVerusDamagePackets(x, y, z, 3.05,0.41999998688697815)
            "damage2" -> sendVerusDamagePackets(x, y, z, 3.35,0.0)
            "damage3" -> sendVerusDamagePackets(x, y, z, 4.0, 0.0)
            "damage4" -> sendVerusDamagePackets(x, y, z, 3.42,0.0)
            "customdamage" -> {
                sendPackets(
                    C04PacketPlayerPosition(x, y + customPacket1Clip.toDouble(), z, false),
                    C04PacketPlayerPosition(x, y + customPacket2Clip.toDouble(), z, false),
                    C04PacketPlayerPosition(x, y + customPacket3Clip.toDouble(), z, true)
                )
            }
        }
    }

    private fun sendVerusDamagePackets(x: Double, y: Double, z: Double, yClip: Double, yOffset: Double) {
        sendPackets(
            C04PacketPlayerPosition(x, y + yClip, z, false),
            C04PacketPlayerPosition(x, y, z, false),
            C04PacketPlayerPosition(x, y + yOffset, z, true)
        )
    }
}
