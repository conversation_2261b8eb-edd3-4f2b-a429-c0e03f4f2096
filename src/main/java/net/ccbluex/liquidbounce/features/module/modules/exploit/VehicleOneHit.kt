/*
 * LiquidBounce Hacked Client
 * A free open source mixin-based injection hacked client for Minecraft using Minecraft Forge.
 * https://github.com/CCBlueX/LiquidBounce/
 */
package net.ccbluex.liquidbounce.features.module.modules.exploit

import net.ccbluex.liquidbounce.event.AttackEvent
import net.ccbluex.liquidbounce.event.handler
import net.ccbluex.liquidbounce.features.module.Category
import net.ccbluex.liquidbounce.features.module.Module
import net.ccbluex.liquidbounce.utils.client.PacketUtils.sendPackets
import net.minecraft.entity.item.EntityBoat
import net.minecraft.entity.item.EntityMinecart
import net.minecraft.network.play.client.C02PacketUseEntity
import net.minecraft.network.play.client.C02PacketUseEntity.Action.ATTACK
import net.minecraft.network.play.client.C0APacketAnimation

object VehicleOneHit :
    Module("VehicleOneHit", Category.EXPLOIT, subjective = true, gameDetecting = false) {

    val onAttack = handler<AttackEvent> { event ->
        if (event.targetEntity is EntityBoat || event.targetEntity is EntityMinecart) {
            repeat(4) {
                sendPackets(
                    C0APacketAnimation(),
                    C02PacketUseEntity(event.targetEntity, ATTACK)
                )
            }
        }
    }

}
