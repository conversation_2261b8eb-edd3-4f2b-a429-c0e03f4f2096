/*
 * LiquidBounce Hacked Client
 * A free open source mixin-based injection hacked client for Minecraft using Minecraft Forge.
 * https://github.com/CCBlueX/LiquidBounce/
 */
package net.ccbluex.liquidbounce.features.module.modules.exploit

import net.ccbluex.liquidbounce.event.*
import net.ccbluex.liquidbounce.features.module.Category
import net.ccbluex.liquidbounce.features.module.Module
import net.ccbluex.liquidbounce.utils.block.block
import net.ccbluex.liquidbounce.utils.block.center
import net.ccbluex.liquidbounce.utils.client.PacketUtils.sendPacket
import net.ccbluex.liquidbounce.utils.client.PacketUtils.sendPackets
import net.ccbluex.liquidbounce.utils.client.chat
import net.ccbluex.liquidbounce.utils.extensions.*
import net.ccbluex.liquidbounce.utils.movement.MovementUtils.forward
import net.ccbluex.liquidbounce.utils.movement.MovementUtils.strafe
import net.ccbluex.liquidbounce.utils.pathfinding.PathUtils.findBlinkPath
import net.ccbluex.liquidbounce.utils.render.RenderUtils.drawFilledBox
import net.ccbluex.liquidbounce.utils.render.RenderUtils.glColor
import net.ccbluex.liquidbounce.utils.render.RenderUtils.renderNameTag
import net.ccbluex.liquidbounce.utils.timing.TickTimer
import net.minecraft.block.BlockAir
import net.minecraft.block.BlockFence
import net.minecraft.block.BlockSnow
import net.minecraft.block.material.Material.air
import net.minecraft.client.renderer.GlStateManager
import net.minecraft.network.Packet
import net.minecraft.network.play.client.C03PacketPlayer
import net.minecraft.network.play.client.C03PacketPlayer.C04PacketPlayerPosition
import net.minecraft.network.play.client.C03PacketPlayer.C06PacketPlayerPosLook
import net.minecraft.network.play.client.C0BPacketEntityAction
import net.minecraft.network.play.client.C0BPacketEntityAction.Action.START_SNEAKING
import net.minecraft.network.play.client.C0BPacketEntityAction.Action.STOP_SNEAKING
import net.minecraft.util.AxisAlignedBB
import net.minecraft.util.BlockPos
import net.minecraft.util.MovingObjectPosition
import net.minecraft.util.Vec3
import org.lwjgl.input.Mouse.isButtonDown
import org.lwjgl.opengl.GL11.*
import java.awt.Color
import java.lang.Math.round
import javax.vecmath.Vector3d

object Teleport : Module("Teleport", Category.EXPLOIT) {
    private val ignoreNoCollision by boolean("IgnoreNoCollision", true)
    private val mode by choices(
        "Mode",
        arrayOf("Tp", "Blink", "Flag", "Rewinside", "OldRewinside", "Spoof", "Minesucht", "AAC3.5.0", "BWRel", "Karhu"),
        "Tp"
    )
    private val button by choices("Button", arrayOf("Left", "Right", "Middle"), "Middle")
    private val needSneak by boolean("NeedSneak", true) { mode !in noSneakModes }

    private val noSneakModes = setOf("Rewinside", "OldRewinside", "AAC3.5.0", "Spoof")

    private val flyTimer = TickTimer()
    private var hadGround = false
    private var fixedY = 0.0
    private val packets = mutableListOf<Packet<*>>()
    private var disableLogger = false
    private var zitter = false
    private var doTeleport = false
    private var freeze = false
    private val freezeTimer = TickTimer()

    private var delay = 0
    private var endPos: BlockPos? = null
    private var objectPosition: MovingObjectPosition? = null

    private var endX = 0.0
    private var endY = 0.0
    private var endZ = 0.0

    override fun onEnable() {
        if (mode == "AAC3.5.0") {
            chat("§c>>> §a§lTeleport §fAAC 3.5.0 §c<<<")
            chat("§cHow to teleport: §aPress $button mouse button.")
            chat("§cHow to cancel teleport: §aDisable teleport module.")
        }
    }

    override fun onDisable() {
        fixedY = 0.0
        delay = 0
        mc.timer.timerSpeed = 1f
        endPos = null
        hadGround = false
        freeze = false
        disableLogger = false
        flyTimer.reset()
        packets.clear()
    }

    val onUpdate = handler<UpdateEvent> {
        val player = mc.thePlayer ?: return@handler
        val world = mc.theWorld ?: return@handler

        val buttonIndex = listOf("Left", "Right", "Middle").indexOf(button)

        if (mode == "AAC3.5.0") {
            freezeTimer.update()

            if (freeze && freezeTimer.hasTimePassed(40)) {
                freezeTimer.reset()
                freeze = false
                state = false
            }

            if (!flyTimer.hasTimePassed(60)) {
                flyTimer.update()

                if (player.onGround) player.jump()
                else {
                    forward(if (zitter) -0.21 else 0.21)
                    zitter = !zitter
                }

                hadGround = false
                return@handler
            }

            if (player.onGround) hadGround = true

            if (!hadGround) return@handler

            if (player.onGround) player.setPositionAndUpdate(
                player.posX, player.posY + 0.2, player.posZ
            )

            player.capabilities.isFlying = false

            if (mc.gameSettings.keyBindJump.isKeyDown) player.motionY = 2.0
            else if (mc.gameSettings.keyBindSneak.isKeyDown) player.motionY = -2.0
            else player.motionY = 0.0

            strafe(2f, true)

            if (isButtonDown(buttonIndex) && !doTeleport) {
                player.setPositionAndUpdate(player.posX, player.posY - 11, player.posZ)

                disableLogger = true
                sendPackets(*packets.toTypedArray())

                freezeTimer.reset()
                freeze = true
            }

            doTeleport = isButtonDown(buttonIndex)
            return@handler
        }

        if (mc.currentScreen == null && isButtonDown(buttonIndex) && delay <= 0) {
            val objectPosition = objectPosition!!
            val block = objectPosition.blockPos.block!!
            endPos = objectPosition.blockPos

            if (endPos!!.block!!.material === air) {
                endPos = null
                return@handler
            }

            val collisionBoundingBox =
                block.getCollisionBoundingBox(world, objectPosition.blockPos, block.defaultState)
            val y = (collisionBoundingBox?.maxY ?: (endPos!!.y + endPos!!.block!!.blockBoundsMaxY)) + fixedY
            chat("§7[§8§lTeleport§7] §3Position was set to §8${endPos!!.x}§3, §8$y§3, §8${endPos!!.z}")

            delay = 6
            endX = endPos!!.x + 0.5
            endY = endPos!!.y + 1.0
            endZ = endPos!!.z + 0.5
        }

        if (delay > 0) --delay

        if (endPos != null && (player.isSneaking || !needSneak || mode in noSneakModes)) when (mode) {
            "Blink" -> {
                // Sneak
                sendPacket(C0BPacketEntityAction(player, STOP_SNEAKING))

                // Teleport
                findBlinkPath(endX, endY, endZ).forEach { vector3d: Vector3d ->
                    sendPacket(C04PacketPlayerPosition(vector3d.x, vector3d.y, vector3d.z, true))
                    player.setPosition(endX, endY, endZ)
                }

                // Sneak
                sendPacket(C0BPacketEntityAction(player, START_SNEAKING))

                // Notify
                chat("§7[§8§lTeleport§7] §3You were teleported to §8$endX§3, §8$endY§3, §8$endZ")
                endPos = null
            }

            "Flag" -> {
                sendPackets( // Sneak
                    C0BPacketEntityAction(player, STOP_SNEAKING),  // Teleport

                    C04PacketPlayerPosition(player.posX, player.posY, player.posZ, true),
                    C04PacketPlayerPosition(endX, endY, endZ, true),
                    C04PacketPlayerPosition(player.posX, player.posY, player.posZ, true),
                    C04PacketPlayerPosition(player.posX, player.posY + 5, player.posZ, true),
                    C04PacketPlayerPosition(endX, endY, endZ, true),
                    C04PacketPlayerPosition(
                        player.posX + 0.5, player.posY, player.posZ + 0.5, true
                    ),  // Sneak

                    C0BPacketEntityAction(player, START_SNEAKING)
                )

                forward(0.04)

                // Notify
                chat("§7[§8§lTeleport§7] §3You were teleported to §8$endX§3, §8$endY§3, §8$endZ")
                endPos = null
            }

            "BWRel" -> {
                player.setPosition(
                    player.posX, player.posY + 9.2507838107252498276, player.posZ
                )
                player.motionY = 1.042026214225532854
                sendPacket(C04PacketPlayerPosition(endX, endY, endZ, true))
            }

            "Rewinside" -> {
                player.motionY = 0.1
                sendPackets(
                    C04PacketPlayerPosition(endX, endY, endZ, true),
                    C04PacketPlayerPosition(player.posX, player.posY + 0.6, player.posZ, true)
                )

                if (player.posX.toInt().toDouble() == endX && player.posY.toInt()
                        .toDouble() == endY && player.posZ.toInt().toDouble() == endZ
                ) {
                    chat("§7[§8§lTeleport§7] §3You were teleported to §8$endX§3, §8$endY§3, §8$endZ")
                    endPos = null
                } else chat("§7[§8§lTeleport§7] §3Teleport try...")
            }

            "OldRewinside" -> {
                player.motionY = 0.1

                sendPackets(
                    C04PacketPlayerPosition(player.posX, player.posY, player.posZ, true),
                    C04PacketPlayerPosition(endX, endY, endZ, true),
                    C04PacketPlayerPosition(player.posX, player.posY, player.posZ, true),
                    C04PacketPlayerPosition(player.posX, player.posY, player.posZ, true),
                    C04PacketPlayerPosition(endX, endY, endZ, true),
                    C04PacketPlayerPosition(player.posX, player.posY, player.posZ, true)
                )

                if (player.posX.toInt().toDouble() == endX && player.posY.toInt()
                        .toDouble() == endY && player.posZ.toInt().toDouble() == endZ
                ) {
                    chat("§7[§8§lTeleport§7] §3You were teleported to §8$endX§3, §8$endY§3, §8$endZ")
                    endPos = null
                } else chat("§7[§8§lTeleport§7] §3Teleport try...")

                forward(0.04)
            }

            "Minesucht" -> {
                sendPacket(C04PacketPlayerPosition(endX, endY, endZ, true))
                chat("§7[§8§lTeleport§7] §3You were teleported to §8$endX§3, §8$endY§3, §8$endZ")
                endPos = null
            }

            "Tp" -> {
                sendPacket(C04PacketPlayerPosition(endX, endY, endZ, true))
                player.setPosition(endX, endY, endZ)
                chat("§7[§8§lTeleport§7] §3You were teleported to §8$endX§3, §8$endY§3, §8$endZ")
                endPos = null
            }

            "Karhu" -> {
                repeat(5) {
                    sendPackets(
                        C04PacketPlayerPosition(endX, endY, endZ, false),
                        C04PacketPlayerPosition(player.posX, player.posY, player.posZ, false)
                    )
                }

                player.setPosition(endX, endY, endZ)

                chat("§7[§8§lTeleport§7] §3You were teleported to §8$endX§3, §8$endY§3, §8$endZ")
                endPos = null
            }
        }
    }

    val onRender3D = handler<Render3DEvent> {
        val player = mc.thePlayer ?: return@handler
        val world = mc.theWorld ?: return@handler

        if (mode == "AAC3.5.0") return@handler

        val lookVec = Vec3(
            player.lookVec.xCoord * 300, player.lookVec.yCoord * 300, player.lookVec.zCoord * 300
        )
        val posVec = Vec3(player.posX, player.posY + 1.62, player.posZ)

        objectPosition =
            player.worldObj.rayTraceBlocks(posVec, posVec + lookVec, false, ignoreNoCollision, false) ?: return@handler
        val blockPos = objectPosition!!.blockPos ?: return@handler
        val block = blockPos.block

        val belowBlockPos = BlockPos(blockPos.x, blockPos.y - 1, blockPos.z)

        fixedY = if (block is BlockFence) (if (world.getCollidingBoundingBoxes(
                player, player.entityBoundingBox.offset(blockPos.center.withY(1.0, true) - player.currPos)
            ).isEmpty()
        ) 0.5 else 0.0) else if (belowBlockPos.block is BlockFence) (if (world.getCollidingBoundingBoxes(
                player, player.entityBoundingBox.offset(blockPos.center - player.currPos)
            ).isNotEmpty() || block!!.getCollisionBoundingBox(world, blockPos, block.defaultState) == null
        ) 0.0 else 0.5 - block.blockBoundsMaxY) else if (block is BlockSnow) block.blockBoundsMaxY - 0.125 else 0.0

        val x = blockPos.x
        val y = (if (block!!.getCollisionBoundingBox(
                world, blockPos, block.defaultState
            ) == null
        ) blockPos.y + block.blockBoundsMaxY else block.getCollisionBoundingBox(
            world, blockPos, block.defaultState
        ).maxY) - 1 + fixedY
        val z = blockPos.z

        if (block !is BlockAir) {
            val renderManager = mc.renderManager

            glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA)
            glEnable(GL_BLEND)
            glLineWidth(2f)
            glDisable(GL_TEXTURE_2D)
            glDisable(GL_DEPTH_TEST)
            glDepthMask(false)
            glColor(
                if (mode == "Minesucht" && player.position.y.toDouble() != y + 1) Color(
                    255, 0, 0, 90
                ) else if (world.getCollidingBoundingBoxes(
                        player, player.entityBoundingBox.offset(
                            x + 0.5 - player.posX, y + 1.0 - player.posY, z + 0.5 - player.posZ
                        )
                    ).isNotEmpty()
                ) Color(255, 0, 0, 90) else Color(0, 255, 0, 90)
            )
            drawFilledBox(
                AxisAlignedBB(
                    x - renderManager.renderPosX,
                    (y + 1) - renderManager.renderPosY,
                    z - renderManager.renderPosZ,
                    x - renderManager.renderPosX + 1,
                    y + 1.2 - renderManager.renderPosY,
                    z - renderManager.renderPosZ + 1
                )
            )
            glEnable(GL_TEXTURE_2D)
            glEnable(GL_DEPTH_TEST)
            glDepthMask(true)
            glDisable(GL_BLEND)

            renderNameTag(
                round(player.getDistance(x + 0.5, y + 1, z + 0.5)).toString() + "m", x + 0.5, y + 1.7, z + 0.5
            )
            GlStateManager.resetColor()
        }
    }

    val onMove = handler<MoveEvent> { event ->
        if (mode == "AAC3.5.0" && freeze) {
            event.zeroXZ()
        }
    }

    val onPacket = handler<PacketEvent> { event ->
        val packet = event.packet

        val player = mc.thePlayer ?: return@handler

        if (disableLogger) return@handler

        if (packet is C03PacketPlayer) {
            when (mode) {
                "Spoof" -> if (endPos != null) {
                    val endPos = endPos!!
                    packet.x = endPos.x + 0.5
                    packet.y = endPos.y + 1.0
                    packet.z = endPos.z + 0.5
                    player.setPosition(endPos.x + 0.5, endPos.y + 1.0, endPos.z + 0.5)
                }

                "AAC3.5.0" -> {
                    if (!flyTimer.hasTimePassed(60)) return@handler

                    event.cancelEvent()

                    if (packet !is C04PacketPlayerPosition && packet !is C06PacketPlayerPosLook) return@handler

                    packets.add(packet)
                }
            }
        }
    }

    override val tag
        get() = mode
}
