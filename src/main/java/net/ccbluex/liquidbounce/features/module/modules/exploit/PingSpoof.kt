/*
 * LiquidBounce Hacked Client
 * A free open source mixin-based injection hacked client for Minecraft using Minecraft Forge.
 * https://github.com/CCBlueX/LiquidBounce/
 */
package net.ccbluex.liquidbounce.features.module.modules.exploit

import net.ccbluex.liquidbounce.event.GameLoopEvent
import net.ccbluex.liquidbounce.event.PacketEvent
import net.ccbluex.liquidbounce.event.WorldEvent
import net.ccbluex.liquidbounce.event.handler
import net.ccbluex.liquidbounce.features.module.Category
import net.ccbluex.liquidbounce.features.module.Module
import net.ccbluex.liquidbounce.features.module.modules.combat.Velocity
import net.ccbluex.liquidbounce.utils.client.PacketUtils
import net.minecraft.network.Packet
import net.minecraft.network.play.client.C0CPacketInput
import net.minecraft.network.play.server.*

object PingSpoof : Module("PingSpoof", Category.EXPLOIT) {

    private val pingOnly by boolean("PingOnly", true)

    private val spoofDelay by int("SpoofDelay", 500, 0..25000)

    private val packetQueue = LinkedHashMap<Packet<*>, Long>()

    override fun onDisable() = reset()

    val onPacket = handler<PacketEvent> { event ->
        val packet = event.packet

        if (event.isCancelled || mc.thePlayer == null)
            return@handler

        if (pingOnly) {
            if (packet is S32PacketConfirmTransaction || packet is S00PacketKeepAlive) {
                event.cancelEvent()

                // Use nano time for the registration time since there are chances
                // the packets can be under the same milliseconds and mess up the order
                synchronized(packetQueue) {
                    packetQueue[packet] = System.currentTimeMillis()
                }
            }
        } else {
            // This should bypass simulation AntiCheat better.
            // Example: GrimAC
            if (packet is S32PacketConfirmTransaction || packet is S00PacketKeepAlive
                || packet is S19PacketEntityStatus || (packet is S12PacketEntityVelocity && !Velocity.delayMode)
                || packet is S08PacketPlayerPosLook || packet is C0CPacketInput
            ) {
                event.cancelEvent()

                // Use nano time for the registration time since there are chances
                // the packets can be under the same milliseconds and mess up the order
                synchronized(packetQueue) {
                    packetQueue[packet] = System.currentTimeMillis()
                }
            }
        }
    }

    val onGameLoop = handler<GameLoopEvent> {
        sendPacketsByOrder(false)
    }

    val onWorld = handler<WorldEvent> {
        packetQueue.clear()
    }

    // Accept packets that have passed the requested delay, then sort by registration time
    private fun sendPacketsByOrder(all: Boolean) =
        synchronized(packetQueue) {
            packetQueue.entries.removeAll { (packet, timestamp) ->
                if (all || timestamp <= (System.currentTimeMillis() - spoofDelay)) {
                    PacketUtils.schedulePacketProcess(packet)
                    true
                } else false
            }
        }

    private fun reset() {
        sendPacketsByOrder(true)

        packetQueue.clear()
    }
}
