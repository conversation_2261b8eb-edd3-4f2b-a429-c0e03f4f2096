/*
 * LiquidBounce Hacked Client
 * A free open source mixin-based injection hacked client for Minecraft using Minecraft Forge.
 * https://github.com/CCBlueX/LiquidBounce/
 */
package net.ccbluex.liquidbounce.features.module.modules.exploit

import net.ccbluex.liquidbounce.event.PacketEvent
import net.ccbluex.liquidbounce.event.UpdateEvent
import net.ccbluex.liquidbounce.event.handler
import net.ccbluex.liquidbounce.features.module.Category
import net.ccbluex.liquidbounce.features.module.Module
import net.ccbluex.liquidbounce.utils.client.PacketUtils.sendPacket
import net.ccbluex.liquidbounce.utils.client.chat
import net.ccbluex.liquidbounce.utils.timing.TickTimer
import net.minecraft.network.play.client.C14PacketTabComplete
import net.minecraft.network.play.server.S3APacketTabComplete

object Plugins : Module("Plugins", Category.EXPLOIT, subjective = true, gameDetecting = false) {

    private val tickTimer = TickTimer()

    override fun onEnable() {
        if (mc.thePlayer == null)
            return

        sendPacket(C14PacketTabComplete("/"))
        tickTimer.reset()
    }

    val onUpdate = handler<UpdateEvent> {
        tickTimer.update()

        if (tickTimer.hasTimePassed(20)) {
            chat("§cPlugins check timed out...")
            tickTimer.reset()
            state = false
        }
    }

    val onPacket = handler<PacketEvent> { event ->
        if (event.packet is S3APacketTabComplete) {
            val s3APacketTabComplete = event.packet

            val plugins = mutableSetOf<String>()
            val commands = s3APacketTabComplete.func_149630_c()

            for (command1 in commands) {
                val command = command1.split(":")

                if (command.size > 1) {
                    val pluginName = command[0].replace("/", "")
                    plugins += pluginName
                }
            }

            if (plugins.isNotEmpty())
                chat("§aPlugins §7(§8" + plugins.size + "§7): §c" + plugins.sorted().joinToString("§7, §c"))
            else
                chat("§cNo plugins found.")
            state = false
            tickTimer.reset()
        }
    }
}
