/*
 * LiquidBounce Hacked Client
 * A free open source mixin-based injection hacked client for Minecraft using Minecraft Forge.
 * https://github.com/CCBlueX/LiquidBounce/
 */
package net.ccbluex.liquidbounce.features.module.modules.exploit

import net.ccbluex.liquidbounce.event.UpdateEvent
import net.ccbluex.liquidbounce.event.handler
import net.ccbluex.liquidbounce.features.module.Category
import net.ccbluex.liquidbounce.features.module.Module
import net.ccbluex.liquidbounce.utils.client.chat
import net.minecraft.client.gui.GuiGameOver

object Ghost : Module("Ghost", Category.EXPLOIT) {

    private var isGhost = false

    val onUpdate = handler<UpdateEvent> {
        if (mc.currentScreen is GuiGameOver) {
            mc.displayGuiScreen(null)
            mc.thePlayer.isDead = false
            mc.thePlayer.health = 20F
            mc.thePlayer.setPositionAndUpdate(mc.thePlayer.posX, mc.thePlayer.posY, mc.thePlayer.posZ)
            isGhost = true

            chat("§cYou are now a ghost.")
        }
    }

    override fun onDisable() {
        if (isGhost && mc.thePlayer != null) {
            mc.thePlayer.respawnPlayer()
            isGhost = false
        }
    }

}