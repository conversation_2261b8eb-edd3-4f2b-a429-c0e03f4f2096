/*
 * LiquidBounce Hacked Client
 * A free open source mixin-based injection hacked client for Minecraft using Minecraft Forge.
 * https://github.com/CCBlueX/LiquidBounce/
 */
package net.ccbluex.liquidbounce.features.module.modules.exploit

import net.ccbluex.liquidbounce.event.PacketEvent
import net.ccbluex.liquidbounce.event.handler
import net.ccbluex.liquidbounce.features.module.Category
import net.ccbluex.liquidbounce.features.module.Module
import net.minecraft.network.play.client.C01PacketChatMessage

object ForceUnicodeChat :
    Module("ForceUnicodeChat", Category.EXPLOIT, subjective = true, gameDetecting = false) {

    val onPacket = handler<PacketEvent> { event ->
        if (event.packet is C01PacketChatMessage) {
            val chatMessage = event.packet
            val message = chatMessage.message

            if (message.startsWith('/')) return@handler

            val stringBuilder = StringBuilder()

            for (c in message.toCharArray())
                if (c.code in 33..128)
                    stringBuilder.append(Character.toChars(c.code + 65248)) else stringBuilder.append(c)

            chatMessage.message = stringBuilder.toString()
        }
    }

}