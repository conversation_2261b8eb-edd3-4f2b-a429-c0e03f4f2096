/*
 * LiquidBounce Hacked Client
 * A free open source mixin-based injection hacked client for Minecraft using Minecraft Forge.
 * https://github.com/CCBlueX/LiquidBounce/
 */
package net.ccbluex.liquidbounce.features.module.modules.exploit

import net.ccbluex.liquidbounce.event.Render3DEvent
import net.ccbluex.liquidbounce.event.UpdateEvent
import net.ccbluex.liquidbounce.event.handler
import net.ccbluex.liquidbounce.features.module.Category
import net.ccbluex.liquidbounce.features.module.Module
import net.ccbluex.liquidbounce.utils.block.block
import net.ccbluex.liquidbounce.utils.client.PacketUtils.sendPacket
import net.ccbluex.liquidbounce.utils.client.PacketUtils.sendPackets
import net.ccbluex.liquidbounce.utils.client.chat
import net.ccbluex.liquidbounce.utils.extensions.toDegreesF
import net.ccbluex.liquidbounce.utils.movement.MovementUtils.forward
import net.ccbluex.liquidbounce.utils.render.RenderUtils.drawFilledBox
import net.ccbluex.liquidbounce.utils.render.RenderUtils.glColor
import net.ccbluex.liquidbounce.utils.render.RenderUtils.renderNameTag
import net.minecraft.block.material.Material.air
import net.minecraft.client.renderer.GlStateManager
import net.minecraft.network.play.client.C03PacketPlayer.C04PacketPlayerPosition
import net.minecraft.util.AxisAlignedBB
import net.minecraft.util.BlockPos
import net.minecraft.util.MovingObjectPosition
import org.lwjgl.input.Mouse.isButtonDown
import org.lwjgl.opengl.GL11.*
import java.awt.Color
import javax.vecmath.Vector3f
import kotlin.math.atan2
import kotlin.math.cos
import kotlin.math.sin
import kotlin.math.sqrt

object ItemTeleport : Module("ItemTeleport", Category.EXPLOIT) {
    private val mode by choices("Mode", arrayOf("New", "Old"), "New")
    private val resetAfterTp by boolean("ResetAfterTP", true)
    private val button by choices("Button", arrayOf("Left", "Right", "Middle"), "Middle")

    private var delay = 0
    private var endPos: BlockPos? = null
    private var objectPosition: MovingObjectPosition? = null

    override fun onDisable() {
        delay = 0
        endPos = null
    }

    val onUpdate = handler<UpdateEvent> {
        if (mc.currentScreen == null && isButtonDown(
                arrayOf(
                    "Left",
                    "Right",
                    "Middle"
                ).indexOf(button)
            ) && delay <= 0
        ) {
            endPos = objectPosition!!.blockPos!!

            if (endPos!!.block!!.material === air) {
                endPos = null
                return@handler
            }

            chat("§7[§8§lItemTeleport§7] §3Position was set to §8${endPos!!.x}§r, §8${endPos!!.y}§r, §8${endPos!!.z}")
            delay = 6
        }

        if (delay > 0) --delay

        mc.thePlayer ?: return@handler

        if (endPos != null && mc.thePlayer.isSneaking) {
            if (!mc.thePlayer.onGround) {
                val endX = endPos!!.x + 0.5
                val endY = (endPos!!.y + 1).toDouble()
                val endZ = endPos!!.z + 0.5

                when (mode) {
                    "Old" -> for (vector3f in vanillaTeleportPositions(endX, endY, endZ, 4.0)) sendPacket(
                        C04PacketPlayerPosition(
                            vector3f.getX().toDouble(),
                            vector3f.getY().toDouble(),
                            vector3f.getZ().toDouble(),
                            false
                        )
                    )

                    "New" -> for (vector3f in vanillaTeleportPositions(endX, endY, endZ, 5.0)) {
                        sendPackets(
                            C04PacketPlayerPosition(mc.thePlayer.posX, mc.thePlayer.posY, mc.thePlayer.posZ, true),
                            C04PacketPlayerPosition(
                                vector3f.x.toDouble(),
                                vector3f.y.toDouble(),
                                vector3f.z.toDouble(),
                                true
                            ),
                            C04PacketPlayerPosition(mc.thePlayer.posX, mc.thePlayer.posY, mc.thePlayer.posZ, true),
                            C04PacketPlayerPosition(
                                mc.thePlayer.posX,
                                mc.thePlayer.posY + 4.0,
                                mc.thePlayer.posZ,
                                true
                            ),
                            C04PacketPlayerPosition(
                                vector3f.x.toDouble(),
                                vector3f.y.toDouble(),
                                vector3f.z.toDouble(),
                                true
                            )
                        )
                        forward(0.04)
                    }
                }
                if (resetAfterTp) endPos = null

                chat("§7[§8§lItemTeleport§7] §3Tried to collect items")
            } else mc.thePlayer.jump()
        }
    }

    val onRender3D = handler<Render3DEvent> { event ->
        objectPosition = mc.thePlayer.rayTrace(1000.0, event.partialTicks)

        if (objectPosition!!.blockPos == null) return@handler

        val x = objectPosition!!.blockPos.x
        val y = objectPosition!!.blockPos.y
        val z = objectPosition!!.blockPos.z

        if (objectPosition!!.blockPos.block!!.material !== air) {
            val renderManager = mc.renderManager

            glBlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA)
            glEnable(GL_BLEND)
            glLineWidth(2f)
            glDisable(GL_TEXTURE_2D)
            glDisable(GL_DEPTH_TEST)
            glDepthMask(false)
            glColor(
                if (objectPosition!!.blockPos.up().block!!.material !== air)
                    Color(255, 0, 0, 90) else Color(0, 255, 0, 90)
            )
            drawFilledBox(
                AxisAlignedBB(
                    x - renderManager.renderPosX,
                    (y + 1) - renderManager.renderPosY,
                    z - renderManager.renderPosZ,
                    x - renderManager.renderPosX + 1,
                    y + 1.2 - renderManager.renderPosY,
                    z - renderManager.renderPosZ + 1
                )
            )
            glEnable(GL_TEXTURE_2D)
            glEnable(GL_DEPTH_TEST)
            glDepthMask(true)
            glDisable(GL_BLEND)

            renderNameTag(
                Math.round(mc.thePlayer.getDistance(x.toDouble(), y.toDouble(), z.toDouble())).toString() + "m",
                x + 0.5,
                y + 1.7,
                z + 0.5
            )
            GlStateManager.resetColor()
        }
    }

    private fun vanillaTeleportPositions(tpX: Double, tpY: Double, tpZ: Double, speed: Double): List<Vector3f> {
        val positions: MutableList<Vector3f> = ArrayList()
        val posX = tpX - mc.thePlayer.posX
        val posZ = tpZ - mc.thePlayer.posZ
        val yaw = atan2(posZ, posX).toDegreesF() - 90f
        var tmpX: Double
        var tmpY = mc.thePlayer.posY
        var tmpZ: Double
        var steps = 1.0

        run {
            var d = speed
            while (d < getDistance(mc.thePlayer.posX, mc.thePlayer.posY, mc.thePlayer.posZ, tpX, tpY, tpZ)) {
                steps++
                d += speed
            }
        }

        var d = speed
        while (d < getDistance(mc.thePlayer.posX, mc.thePlayer.posY, mc.thePlayer.posZ, tpX, tpY, tpZ)) {
            tmpX = mc.thePlayer.posX - (sin(Math.toRadians(yaw.toDouble())) * d)
            tmpZ = mc.thePlayer.posZ + cos(Math.toRadians(yaw.toDouble())) * d
            tmpY -= (mc.thePlayer.posY - tpY) / steps
            positions.add(Vector3f(tmpX.toFloat(), tmpY.toFloat(), tmpZ.toFloat()))
            d += speed
        }

        positions.add(Vector3f(tpX.toFloat(), tpY.toFloat(), tpZ.toFloat()))

        return positions
    }

    private fun getDistance(x1: Double, y1: Double, z1: Double, x2: Double, y2: Double, z2: Double): Double {
        val d0 = x1 - x2
        val d1 = y1 - y2
        val d2 = z1 - z2
        return sqrt(d0 * d0 + d1 * d1 + d2 * d2)
    }
}
