/*
 * LiquidBounce Hacked Client
 * A free open source mixin-based injection hacked client for Minecraft using Minecraft Forge.
 * https://github.com/CCBlueX/LiquidBounce/
 */
package net.ccbluex.liquidbounce.features.module.modules.exploit

import net.ccbluex.liquidbounce.event.PacketEvent
import net.ccbluex.liquidbounce.event.handler
import net.ccbluex.liquidbounce.features.module.Category
import net.ccbluex.liquidbounce.features.module.Module
import net.minecraft.network.play.client.C0DPacketCloseWindow

object MoreCarry : Module("MoreCarry", Category.EXPLOIT) {

    val onPacket = handler<PacketEvent> { event ->
        if (event.packet is C0DPacketCloseWindow) {
            event.cancelEvent()
        }
    }

}