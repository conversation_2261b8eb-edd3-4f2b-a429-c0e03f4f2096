/*
 * LiquidBounce Hacked Client
 * A free open source mixin-based injection hacked client for Minecraft using Minecraft Forge.
 * https://github.com/CCBlueX/LiquidBounce/
 */
package net.ccbluex.liquidbounce.features.module.modules.exploit

import net.ccbluex.liquidbounce.features.module.Category
import net.ccbluex.liquidbounce.features.module.Module
import net.ccbluex.liquidbounce.utils.client.PacketUtils.sendPackets
import net.ccbluex.liquidbounce.utils.extensions.toRadiansD
import net.minecraft.network.play.client.C03PacketPlayer.C04PacketPlayerPosition
import kotlin.math.cos
import kotlin.math.sin

object Clip : Module("Clip", Category.EXPLOIT, canBeEnabled = false) {

    private val mode by choices("Mode", arrayOf("Teleport", "Flag"), "Teleport")
    private val horizontal by float("Horizontal", 0F, -10F..10F)
    private val vertical by float("Vertical", 5F, -10F..10F)

    override fun onEnable() {
        val thePlayer = mc.thePlayer ?: return

        val yaw = thePlayer.rotationYaw.toRadiansD()
        val x = -sin(yaw) * horizontal
        val z = cos(yaw) * horizontal

        when (mode.lowercase()) {
            "teleport" -> thePlayer.setPosition(
                thePlayer.posX + x, thePlayer.posY + vertical,
                thePlayer.posZ + z
            )

            "flag" -> {
                sendPackets(
                    C04PacketPlayerPosition(mc.thePlayer.posX, mc.thePlayer.posY, mc.thePlayer.posZ, true),
                    C04PacketPlayerPosition(0.5, 0.0, 0.5, true),
                    C04PacketPlayerPosition(mc.thePlayer.posX, mc.thePlayer.posY, mc.thePlayer.posZ, true),
                    C04PacketPlayerPosition(
                        mc.thePlayer.posX + x,
                        mc.thePlayer.posY + vertical,
                        mc.thePlayer.posZ + z,
                        true
                    ),
                    C04PacketPlayerPosition(0.5, 0.0, 0.5, true),
                    C04PacketPlayerPosition(mc.thePlayer.posX + 0.5, mc.thePlayer.posY, mc.thePlayer.posZ + 0.5, true)
                )

                mc.thePlayer.setPosition(
                    mc.thePlayer.posX + -sin(yaw) * 0.04, mc.thePlayer.posY,
                    mc.thePlayer.posZ + cos(yaw) * 0.04
                )
            }
        }
    }

}