/*
 * LiquidBounce Hacked Client
 * A free open source mixin-based injection hacked client for Minecraft using Minecraft Forge.
 * https://github.com/CCBlueX/LiquidBounce/
 */
package net.ccbluex.liquidbounce.features.module.modules.exploit

import net.ccbluex.liquidbounce.event.UpdateEvent
import net.ccbluex.liquidbounce.event.handler
import net.ccbluex.liquidbounce.features.module.Category
import net.ccbluex.liquidbounce.features.module.Module

/**
 * Similar usage to NextGen AntiExploit Modules
 *
 * <AUTHOR>
 */
object AntiExploit : Module("AntiExploit", Category.EXPLOIT) {

    var itemMax = 0
    var arrowMax = 0

    val cancelExplosionMotion by boolean("CancelExplosionMotion", true)
    val cancelExplosionStrength by boolean("CancelExplosionStrength", true)
    val cancelExplosionRadius by boolean("CancelExplosionRadius", true)
    val limitParticlesAmount by boolean("LimitParticlesAmount", true)
    val limitParticlesSpeed by boolean("LimitParticlesSpeed", true)
    val limitedEntitySpawn by boolean("LimitedEntitySpawn", true)
    val maxItemDropped by int("MaxItemsDropped", 1000, 10..5000) { limitedEntitySpawn }
    val maxArrowsSpawned by int("MaxArrowsSpawned", 500, 10..5000) { limitedEntitySpawn }
    val cancelDemo by boolean("CancelDemoGUI", true)

    val warn by choices("Warn", arrayOf("Off", "Chat", "Notification"), "Chat")

    val onUpdate = handler<UpdateEvent> {
        val player = mc.thePlayer ?: return@handler

        if (player.ticksExisted % 500 == 0) {
            arrowMax = 0
            itemMax = 0
        }
    }

    override fun onDisable() {
        arrowMax = 0
        itemMax = 0
    }
}