package net.ccbluex.liquidbounce.injection.forge.mixins.gui;

import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.GuiOptionSlider;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin(GuiOptionSlider.class)
public class MixinGuiOptionSlider {

    @Inject(method = "mouseDragged", at = @At(value = "INVOKE", target = "Lnet/minecraft/client/renderer/texture/TextureManager;bindTexture(Lnet/minecraft/util/ResourceLocation;)V", shift = At.Shift.BEFORE), cancellable = true)
    public void cancelRectangleDrawing(Minecraft p_mouseDragged_1_, int p_mouseDragged_2_, int p_mouseDragged_3_, CallbackInfo ci) {
        ci.cancel();
    }
}
