C00PacketLoginStart
C01PacketEncryptionResponse
C00PacketServerQuery
C01PacketPing
C00PacketKeepAlive
C01PacketChatMessage
C02PacketUseEntity
C03PacketPlayer
C04PacketPlayerPosition
C05PacketPlayerLook
C06PacketPlayerPosLook
C07PacketPlayerDigging
C08PacketPlayerBlockPlacement
C09PacketHeldItemChange
C0APacketAnimation
C0BPacketEntityAction
C0CPacketInput
C0DPacketCloseWindow
C0EPacketClickWindow
C0FPacketConfirmTransaction
C10PacketCreativeInventoryAction
C11PacketEnchantItem
C12PacketUpdateSign
C13PacketPlayerAbilities
C14PacketTabComplete
C15PacketClientSettings
C16PacketClientStatus
C17PacketCustomPayload
C18PacketSpectate
C19PacketResourcePackStatus
C00Handshake
S00PacketDisconnect
S01PacketEncryptionRequest
S02PacketLoginSuccess
S03PacketEnableCompression
S00PacketServerInfo
S01PacketPong
S00PacketKeepAlive
S01PacketJoinGame
S02PacketChat
S03PacketTimeUpdate
S04PacketEntityEquipment
S05PacketSpawnPosition
S06PacketUpdateHealth
S07PacketRespawn
S08PacketPlayerPosLook
S09PacketHeldItemChange
S0APacketUseBed
S0BPacketAnimation
S0CPacketSpawnPlayer
S0DPacketCollectItem
S0EPacketSpawnObject
S0FPacketSpawnMob
S10PacketSpawnPainting
S11PacketSpawnExperienceOrb
S12PacketEntityVelocity
S13PacketDestroyEntities
S14PacketEntity
S15PacketEntityRelMove
S16PacketEntityLook
S17PacketEntityLookMove
S18PacketEntityTeleport
S19PacketEntityHeadLook
S19PacketEntityStatus
S1BPacketEntityAttach
S1CPacketEntityMetadata
S1DPacketEntityEffect
S1EPacketRemoveEntityEffect
S1FPacketSetExperience
S20PacketEntityProperties
S21PacketChunkData
S22PacketMultiBlockChange
S23PacketBlockChange
S24PacketBlockAction
S25PacketBlockBreakAnim
S26PacketMapChunkBulk
S27PacketExplosion
S28PacketEffect
S29PacketSoundEffect
S2APacketParticles
S2BPacketChangeGameState
S2CPacketSpawnGlobalEntity
S2DPacketOpenWindow
S2EPacketCloseWindow
S2FPacketSetSlot
S30PacketWindowItems
S31PacketWindowProperty
S32PacketConfirmTransaction
S33PacketUpdateSign
S34PacketMaps
S35PacketUpdateTileEntity
S36PacketSignEditorOpen
S37PacketStatistics
S38PacketPlayerListItem
S39PacketPlayerAbilities
S3APacketTabComplete
S3BPacketScoreboardObjective
S3CPacketUpdateScore
S3DPacketDisplayScoreboard
S3EPacketTeams
S3FPacketCustomPayload
S40PacketDisconnect
S41PacketServerDifficulty
S42PacketCombatEvent
S43PacketCamera
S44PacketWorldBorder
S45PacketTitle
S46PacketSetCompressionLevel
S47PacketPlayerListHeaderFooter
S48PacketResourcePackSend
S49PacketUpdateEntityNBT