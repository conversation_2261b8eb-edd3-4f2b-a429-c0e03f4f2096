# Minecraft
public net.minecraft.client.Minecraft field_71428_T # timer
public-f net.minecraft.client.Minecraft field_71449_j # session
public net.minecraft.client.Minecraft field_71467_ac # rightClickDelayTimer
public net.minecraft.client.Minecraft func_147116_af()V # clickMouse
public net.minecraft.client.Minecraft func_147121_ag()V # rightClickMouse
public net.minecraft.client.Minecraft func_175594_ao()V # setWindowTitle

# C00Handshake
public net.minecraft.network.handshake.client.C00Handshake field_149598_b # ip
public net.minecraft.network.handshake.client.C00Handshake field_149599_c # port

# C01PacketChatMessage
public net.minecraft.network.play.client.C01PacketChatMessage field_149440_a # message

# C03PacketPlayer
public net.minecraft.network.play.client.C03PacketPlayer field_149479_a # x
public net.minecraft.network.play.client.C03PacketPlayer field_149477_b # y
public net.minecraft.network.play.client.C03PacketPlayer field_149478_c # z
public net.minecraft.network.play.client.C03PacketPlayer field_149476_e # yaw
public net.minecraft.network.play.client.C03PacketPlayer field_149473_f # pitch
public net.minecraft.network.play.client.C03PacketPlayer field_149474_g # onGround
public net.minecraft.network.play.client.C03PacketPlayer field_149481_i # rotating

# C17PacketCustomPayload
public net.minecraft.network.play.client.C17PacketCustomPayload field_149561_c # data

# Entity
public net.minecraft.entity.Entity field_70134_J # isInWeb

# EntityPlayer
public net.minecraft.entity.player.EntityPlayer field_71072_f # itemInUseCount
public net.minecraft.entity.player.EntityPlayer field_71083_bS # sleeping
public net.minecraft.entity.player.EntityPlayer field_71076_b # sleepTimer
public net.minecraft.entity.player.EntityPlayer field_71102_ce # speedInAir

# EntityPlayerSP
public net.minecraft.client.entity.EntityPlayerSP field_110321_bQ # horseJumpPower
public net.minecraft.client.entity.EntityPlayerSP field_110320_a # horseJumpPowerCounter
public net.minecraft.client.entity.EntityPlayerSP field_175171_bO # serverSprintState

# EntityRenderer
public net.minecraft.client.renderer.EntityRenderer func_175069_a(Lnet/minecraft/util/ResourceLocation;)V # loadShader
public net.minecraft.client.renderer.EntityRenderer func_78479_a(FI)V # setupCameraTransform

# GuiChest
public net.minecraft.client.gui.inventory.GuiChest field_147018_x # inventoryRows
public net.minecraft.client.gui.inventory.GuiChest field_147015_w # lowerChestInventory

# GuiContainer
public net.minecraft.client.gui.inventory.GuiContainer func_146984_a(Lnet/minecraft/inventory/Slot;III)V # handleMouseClick
public net.minecraft.client.gui.inventory.GuiContainerCreative func_146984_a(Lnet/minecraft/inventory/Slot;III)V # handleMouseClick

# GuiGameOver
public net.minecraft.client.gui.GuiGameOver field_146347_a # enableButtonsTimer

# GuiScreen
public net.minecraft.client.gui.GuiScreen field_146289_q # fontRendererObj
public net.minecraft.client.gui.GuiScreen field_146292_n # buttonList
public net.minecraft.client.gui.GuiScreen func_175272_a(Lnet/minecraft/util/IChatComponent;II)V # handleComponentHover

# GuiSlot
# Causes Issues with the MC-Code
# public net.minecraft.client.gui.GuiSlot func_148144_a(IZII)V # elementClicked

# ItemBucket
public net.minecraft.item.ItemBucket field_77876_a # isFull

# KeyBinding
public net.minecraft.client.settings.KeyBinding field_74513_e # pressed

# PlayerControllerMP
public net.minecraft.client.multiplayer.PlayerControllerMP field_78770_f # curBlockDamageMP
public net.minecraft.client.multiplayer.PlayerControllerMP field_78781_i # blockHitDelay

# RenderMananger
public net.minecraft.client.renderer.entity.RenderManager field_78725_b # renderPosX
public net.minecraft.client.renderer.entity.RenderManager field_78726_c # renderPosY
public net.minecraft.client.renderer.entity.RenderManager field_78723_d # renderPosZ

# S2EPacketCloseWindow
public net.minecraft.network.play.server.S2EPacketCloseWindow field_148896_a # windowId

# S08PacketPlayerPosLook
public net.minecraft.network.play.server.S08PacketPlayerPosLook field_148936_d # yaw
public net.minecraft.network.play.server.S08PacketPlayerPosLook field_148937_e # pitch

# S12PacketEntityVelocity
public net.minecraft.network.play.server.S12PacketEntityVelocity field_149415_b # motionX
public net.minecraft.network.play.server.S12PacketEntityVelocity field_149416_c # motionY
public net.minecraft.network.play.server.S12PacketEntityVelocity field_149414_d # motionZ

# PacketUtils
public net.minecraft.network.NetworkManager func_150733_h()V # flushOutboundQueue
public net.minecraft.network.NetworkManager func_150732_b(Lnet/minecraft/network/Packet;[Lio/netty/util/concurrent/GenericFutureListener;)V # dispatchPacket
public net.minecraft.network.NetworkManager field_181680_j # readWriteLock
public net.minecraft.network.NetworkManager field_150745_j # outboundPacketsQueue
public net.minecraft.network.NetworkManager$InboundHandlerTuplePacketListener

public net.minecraft.client.multiplayer.PlayerControllerMP field_78777_l # currentPlayerItem

public net.minecraft.client.settings.KeyBinding field_151474_i # pressTime

# FontRenderer
public net.minecraft.client.gui.FontRenderer func_78277_a(CZ)F # drawUnicodeChar
public net.minecraft.client.gui.FontRenderer field_78295_j # posX
public net.minecraft.client.gui.FontRenderer field_78296_k # posY

# S27PacketExplosion
public net.minecraft.network.play.server.S27PacketExplosion field_149152_f # motionX
public net.minecraft.network.play.server.S27PacketExplosion field_149153_g # motionY
public net.minecraft.network.play.server.S27PacketExplosion field_149159_h # motionZ

# S19PacketEntityStatus
public net.minecraft.network.play.server.S19PacketEntityStatus field_149164_a # entityId

# S14PacketEntity
public net.minecraft.network.play.server.S14PacketEntity field_149074_a # entityId

public net.minecraft.entity.EntityLivingBase field_70703_bu # isJumping

public net.minecraft.client.Minecraft func_147115_a(Z)V # sendClickBlockToController

public net.minecraft.entity.Entity field_70150_b # nextStepDistance

public net.minecraft.entity.Entity field_70151_c # fire

public net.minecraft.entity.EntityLivingBase field_70773_bE # jumpTicks

public-f net.minecraft.util.Vec3 field_72450_a # xCoord
public-f net.minecraft.util.Vec3 field_72448_b # yCoord
public-f net.minecraft.util.Vec3 field_72449_c # zCoord

public net.minecraft.entity.EntityLivingBase field_70709_bj # newPosX
public net.minecraft.entity.EntityLivingBase field_70710_bk # newPosY
public net.minecraft.entity.EntityLivingBase field_110152_bk # newPosZ

public net.minecraft.client.gui.GuiScreenOptionsSounds$Button

public net.minecraft.client.gui.GuiOptionSlider field_146134_p # sliderValue

public net.minecraft.client.multiplayer.PlayerControllerMP func_78750_j()V # syncCurrentPlayItem

public net.minecraft.entity.player.EntityPlayer field_71074_e # itemInUse

public net.minecraft.client.gui.GuiSlot func_148131_a(I)Z # isSelected

public net.minecraft.client.renderer.entity.Render field_76989_e # shadowSize

public net.minecraft.client.Minecraft field_71429_W # leftClickCounter

public net.minecraft.client.renderer.ItemRenderer field_78453_b # itemToRender

public net.minecraft.client.renderer.entity.Render func_110775_a(Lnet/minecraft/entity/Entity;)Lnet/minecraft/util/ResourceLocation; # getEntityTexture