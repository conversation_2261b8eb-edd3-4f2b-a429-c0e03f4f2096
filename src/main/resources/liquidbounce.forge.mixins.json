{"required": true, "package": "net.ccbluex.liquidbounce.injection.forge.mixins", "refmap": "liquidbounce.mixins.refmap.json", "compatibilityLevel": "JAVA_8", "mixins": ["block.MixinBlock", "block.MixinBlockAnvil", "block.MixinBlockLadder", "block.MixinBlockLiquid", "block.MixinBlockModelRenderer", "block.MixinBlockSoulSand", "client.MixinGameSettings", "client.MixinMinecraft", "client.MixinMovementInput", "client.MixinMovementInputFromOptions", "client.MixinProfiler", "client.MixinResourcePackRepository", "entity.MixinAbstractClientPlayer", "entity.MixinEntity", "entity.MixinEntityLivingBase", "entity.MixinEntityPlayer", "entity.MixinEntityPlayerSP", "entity.MixinInventoryPlayer", "entity.MixinPlayerControllerMP", "gui.<PERSON>", "gui.<PERSON>ui<PERSON>", "gui.MixinGuiButtonExt", "gui.MixinGuiChat", "gui.MixinGuiConnecting", "gui.MixinGuiDisconnected", "gui.MixinGuiDownloadTerrain", "gui.MixinGuiEditSign", "gui.MixinGuiInGame", "gui.MixinGuiIngameMenu", "gui.MixinGuiKeyBindingList", "gui.MixinGuiMultiplayer", "gui.MixinGuiNewChat", "gui.MixinGuiOptionSlider", "gui.MixinGuiScreen", "gui.MixinGuiScreenOptionsSoundsButton", "gui.MixinGuiSlider", "gui.MixinGuiSlot", "gui.MixinGuiSpectator", "gui.MixinServerSelectionList", "item.MixinItem", "item.MixinItemRenderer", "item.MixinMixinItemStack", "network.MixinNetHandlerPlayClient", "network.MixinNetworkManager", "network.MixinNetworkPlayerInfo", "packets.MixinC00Handshake", "render.MixinEffectRenderer", "<PERSON>.MixinEntity<PERSON><PERSON>er", "<PERSON>.<PERSON>in<PERSON><PERSON><PERSON>", "render.MixinLayerHeldItem", "render.MixinModelBiped", "<PERSON>.<PERSON>in<PERSON>", "render.MixinRenderEntityItem", "render.MixinRendererLivingEntity", "render.MixinRenderManager", "render.MixinRenderPlayer", "render.MixinTileEntityChestRenderer", "render.MixinTileEntityItemStackRenderer", "render.MixinTileEntityMobSpawnerRenderer", "render.MixinTileEntityRendererDispatcher", "render.MixinVisGraph", "resources.MixinSkinManager", "tweaks.MixinAnvilChunkLoader", "tweaks.MixinMinecraftServer", "world.MixinChunk", "world.MixinWorld", "world.MixinWorldClient"], "client": ["block.MixinBlockSlime", "gui.MixinGuiAchievement", "gui<PERSON>", "gui.MixinGuiTextField", "render.MixinItemRenderer", "render.MixinRenderGlobal", "tweaks.MixinEntityFX"]}